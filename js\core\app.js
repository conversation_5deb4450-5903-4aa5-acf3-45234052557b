/**
 * Main Application Module
 * Initializes and manages the dependency visualizer application using mindmap-vanilla.js
 */

import DependencyScanner from './scanner.js';
import DependencyDataModel from './data-model.js';
import DOMUtils from '../utils/dom-utils.js';

// The MindmapProject class will be available globally via window.MindmapProject

class DependencyVisualizerApp {
    constructor(options = {}) {
        // Configuration
        this.config = {
            container: options.container || 'visualization-container',
            detailsPanel: options.detailsPanel || 'details-panel-container',
            scannerWorkerPath: options.scannerWorkerPath || 'js/core/scanner.worker.js',
            ...options
        };

        // Components
        this.scanner = null;
        this.dataModel = null;
        this.mindmapVisualizer = null;
        
        // DOM elements
        this.container = null;
        this.detailsPanel = null;
        
        // State
        this.isInitialized = false;
        this.isScanning = false;
        
        // Bind methods
        this.initialize = this.initialize.bind(this);
        this.handleFileUpload = this.handleFileUpload.bind(this);
        this.handleNodeSelection = this.handleNodeSelection.bind(this);
        this.showDetailsPanel = this.showDetailsPanel.bind(this);
        this.hideDetailsPanel = this.hideDetailsPanel.bind(this);
    }

    /**
     * Initialize the application
     * @returns {DependencyVisualizerApp} - This instance for chaining
     */
    initialize() {
        if (this.isInitialized) return this;
        
        // Get DOM elements
        this.container = typeof this.config.container === 'string'
            ? DOMUtils.getById(this.config.container)
            : this.config.container;
            
        this.detailsPanel = typeof this.config.detailsPanel === 'string'
            ? DOMUtils.getById(this.config.detailsPanel)
            : this.config.detailsPanel;
        
        // Initialize components
        this.scanner = new DependencyScanner({
            workerPath: this.config.scannerWorkerPath
        });
        
        this.dataModel = new DependencyDataModel();
        
        // Initialize the mindmap visualizer using mindmap-vanilla.js
        this.initializeMindmapVisualizer();
        
        // Set up event listeners
        this.setupEventListeners();
        
        this.isInitialized = true;
        return this;
    }

    /**
     * Initialize the mindmap visualizer using mindmap-vanilla.js
     */
    initializeMindmapVisualizer() {
        if (!this.container) {
            console.error('Main container is undefined, cannot create visualizer');
            return;
        }

        // Clear container
        this.container.innerHTML = '';
        
        // Check if MindmapProject is available
        if (typeof window.MindmapProject === 'undefined') {
            console.error('MindmapProject class not available. Make sure mindmap-vanilla.js is loaded.');
            return;
        }

        // Create SVG element for the mindmap
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('id', 'mindmap-svg');
        svg.setAttribute('width', '100%');
        svg.setAttribute('height', '100%');
        svg.setAttribute('viewBox', '0 0 1100 500'); // Updated for improved spacing layout
        svg.style.background = '#1f2937';
        svg.style.borderRadius = '8px';
        
        this.container.appendChild(svg);
        
        // Create mindmap instance
        this.mindmapVisualizer = new window.MindmapProject(svg);

        // Render the initial mindmap with hardcoded data
        this.mindmapVisualizer.render();

        console.log('Mindmap visualizer initialized and rendered successfully');
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Scanner events
        document.addEventListener('scanner:progress', this.handleScanProgress.bind(this));
        document.addEventListener('scanner:complete', this.handleScanComplete.bind(this));
        document.addEventListener('scanner:error', this.handleScanError.bind(this));
        
        // Visualizer events
        document.addEventListener('visualizer:nodeSelected', this.handleNodeSelection);
        
        // UI events
        const fileInput = DOMUtils.getById('file-input');
        if (fileInput) {
            fileInput.addEventListener('change', this.handleFileUpload);
        }
        
        const switchButtons = document.querySelectorAll('.visualizer-switch');
        switchButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const visualizer = event.target.dataset.visualizer;
                if (visualizer) {
                    this.switchVisualizer(visualizer);
                }
            });
        });
        
        const closeDetailsButton = document.querySelector('.close-button');
        if (closeDetailsButton) {
            closeDetailsButton.addEventListener('click', this.hideDetailsPanel);
        }
        
        // Handle window resize
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    /**
     * Handle file upload event
     * @param {Event} event - File input change event
     */
    async handleFileUpload(event) {
        const files = event.target.files;
        if (!files || files.length === 0) return;
        
        this.isScanning = true;
        
        // Show loading indicator
        this.showLoadingIndicator('Scanning files...');
        
        try {
            // Read files
            const filePromises = Array.from(files).map(file => {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        resolve({
                            path: file.webkitRelativePath || file.name,
                            content: reader.result
                        });
                    };
                    reader.onerror = reject;
                    reader.readAsText(file);
                });
            });
            
            const fileContents = await Promise.all(filePromises);
            
            // Start scanning
            const rootPath = '';
            const results = await this.scanner.startScan(fileContents, rootPath);
            
            // Process results
            this.dataModel.loadData(results);
            
            // Update visualization
            this.updateVisualization(this.dataModel);
            
        } catch (error) {
            console.error('Error scanning files:', error);
            this.showError(`Error scanning files: ${error.message}`);
        } finally {
            this.isScanning = false;
            this.hideLoadingIndicator();
        }
    }

    /**
     * Handle scan progress event
     * @param {CustomEvent} event - Progress event
     */
    handleScanProgress(event) {
        const { processed, total, percentage, phase } = event.detail;
        console.log(`Scan progress: ${phase} - ${processed}/${total} (${percentage}%)`);
    }
    
    /**
     * Handle scan complete event
     * @param {Event} event - Scan complete event
     */
    handleScanComplete(event) {
        this.isScanning = false;
        
        if (event.detail && event.detail.data) {
            this.dataModel.loadData(event.detail.data);
            
            this.updateVisualization(this.dataModel);
            
            // Dispatch event
            DOMUtils.dispatchCustomEvent('app:scanComplete', {
                data: event.detail.data
            });
        }
    }
    
    /**
     * Process scan results from the worker
     * @param {Object} scanResults - The scan results from the worker
     */
    processScanResults(scanResults) {
        if (scanResults) {
            console.log('App.processScanResults - Raw scanResults:', scanResults);

            // The scanResults object from main.js has nodes and links (as edges) nested under a 'graph' property.
            // DataModel expects a flat structure: { nodes: [], links: [], metadata: {} }
            const rawNodes = scanResults.graph?.nodes || [];
            const rawEdges = scanResults.graph?.edges || [];

            console.log('App.processScanResults - Raw nodes:', rawNodes.length, 'nodes');
            console.log('App.processScanResults - Raw edges:', rawEdges.length, 'edges');
            console.log('App.processScanResults - First few edges:', rawEdges.slice(0, 3));

            // Transform nodes to ensure they have proper IDs and preserve all scanner data
            const transformedNodes = rawNodes.map(node => ({
                ...node, // Preserve all original scanner data (imports, exports, functions, etc.)
                id: node.id !== undefined ? String(node.id) : String(node.path), // Ensure ID is a string
                name: node.name || node.path?.split('/').pop() || 'Unknown',
                path: node.path || node.id,
                // Ensure these arrays exist even if empty
                imports: node.imports || [],
                exports: node.exports || [],
                functions: node.functions || [],
                variables: node.variables || [],
                classes: node.classes || [],
                events: node.events || [],
                dependencies: node.dependencies || [],
                dependents: node.dependents || []
            }));

            // Transform edges to use source/target format that mindmap expects
            // Ensure the source and target IDs match the node IDs exactly
            const transformedLinks = rawEdges
                .map(edge => ({
                    source: String(edge.source || edge.from),
                    target: String(edge.target || edge.to),
                    type: edge.type || 'import',
                    names: edge.names || []
                }))
                .filter(link => {
                    // Only include links where both source and target nodes exist
                    const sourceExists = transformedNodes.some(node => node.id === link.source);
                    const targetExists = transformedNodes.some(node => node.id === link.target);
                    if (!sourceExists || !targetExists) {
                        console.warn('Filtering out link with missing nodes:', link);
                    }
                    return sourceExists && targetExists;
                });

            console.log('App.processScanResults - Transformed nodes:', transformedNodes.length);
            console.log('App.processScanResults - Transformed links:', transformedLinks.length);
            console.log('App.processScanResults - Sample transformed links:', transformedLinks.slice(0, 3));

            const dataForModel = {
                nodes: transformedNodes,
                links: transformedLinks,
                metadata: scanResults.metadata || scanResults.graph?.metadata || {}
            };

            // Ensure dataModel is initialized and has loadData
            if (this.dataModel && typeof this.dataModel.loadData === 'function') {
                this.dataModel.loadData(dataForModel);
                console.log('App.processScanResults - Data loaded into model. Nodes count:', this.dataModel.getNodes().length);
                console.log('App.processScanResults - Data loaded into model. Links count:', this.dataModel.getLinks().length);
            } else {
                console.error('DataModel is not properly initialized or loadData method is missing in processScanResults.', this.dataModel);
                this.showError('Critical error: Data model issue during processing.');
                return;
            }
            
            console.log('App.processScanResults - About to update visualization');
            this.updateVisualization(this.dataModel);
            
            // Force additional renders to ensure connections are drawn
            setTimeout(() => {
                console.log('App.processScanResults - Forcing delayed render');
                if (this.mindmapVisualizer) {
                    this.mindmapVisualizer.render();
                }
            }, 200);
            
            // Clear scan results object from memory after processing
            // Data is now safely stored in IndexedDB and loaded into the data model
            if (scanResults.fileContents) {
                console.log('App.processScanResults: Clearing file contents from scanResults object after processing - data is now in IndexedDB and data model');
                scanResults.fileContents = {};
            }

            // Clear other large data structures that are now in the data model
            if (scanResults.files) {
                console.log('App.processScanResults: Clearing files data from scanResults object after processing - data is now in data model');
                scanResults.files = {};
            }

            if (scanResults.graph) {
                console.log('App.processScanResults: Clearing graph data from scanResults object after processing - data is now in data model');
                scanResults.graph.nodes = [];
                scanResults.graph.edges = [];
            }

            // Dispatch event
            DOMUtils.dispatchCustomEvent('app:scanComplete', {
                data: scanResults
            });
        }
    }

    /**
     * Handle scan error event
     * @param {CustomEvent} event - Error event
     */
    handleScanError(event) {
        const { message } = event.detail;
        console.error('Scan error:', message);
        this.showError(`Scan error: ${message}`);
        this.hideLoadingIndicator();
    }

    /**
     * Handle node selection event
     * @param {CustomEvent} event - Node selection event
     */
    handleNodeSelection(event) {
        const { node } = event.detail;
            
        if (node) {
            this.showNodeDetails(node);
        } else {
            this.hideDetailsPanel();
        }
    }

    /**
     * Handle window resize event
     */
    handleResize() {
        // Mindmap handles resize automatically via SVG viewBox
        if (this.mindmapVisualizer) {
            this.mindmapVisualizer.render();
        }
    }

    /**
     * Update the mindmap with new data and apply auto-alignment
     * @param {Object} dataModel - Data model with dependency information
     */
    updateVisualization(dataModel) {
        if (!this.mindmapVisualizer) {
            console.warn('Mindmap visualizer not initialized');
            return;
        }

        if (dataModel && typeof dataModel.getNodes === 'function' && typeof dataModel.getLinks === 'function') {
            console.log('App.updateVisualization - Updating with data model:', {
                nodes: dataModel.getNodes().length,
                links: dataModel.getLinks().length
            });
            
            // Set the data model which will trigger layout processing with auto-alignment
            this.mindmapVisualizer.setDataModel(dataModel);
            
            // Force immediate render
            console.log('App.updateVisualization - Forcing immediate render');
            this.mindmapVisualizer.render();
            
            // Force additional renders to ensure connections are drawn
            setTimeout(() => {
                console.log('App.updateVisualization - Forcing delayed render 1');
                this.mindmapVisualizer.render();
            }, 50);
            
            setTimeout(() => {
                console.log('App.updateVisualization - Forcing delayed render 2');
                this.mindmapVisualizer.render();
            }, 150);
        } else {
            console.warn('Invalid data model provided to updateVisualization');
        }
    }

    /**
     * Render the current visualization
     */
    render() {
        if (this.mindmapVisualizer) {
            this.mindmapVisualizer.render();
        }
    }

    /**
     * Toggle minimap visibility
     */
    toggleMinimap() {
        if (this.mindmapVisualizer && typeof this.mindmapVisualizer.toggleMinimap === 'function') {
            this.mindmapVisualizer.toggleMinimap();
        }
    }

    /**
     * Export visualization as SVG
     */
    exportSVG() {
        if (this.mindmapVisualizer && this.mindmapVisualizer.svgElement) {
            const svgData = new XMLSerializer().serializeToString(this.mindmapVisualizer.svgElement);
            const blob = new Blob([svgData], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'dependency-mindmap.svg';
            link.click();
            URL.revokeObjectURL(url);
        }
    }

    /**
     * Export visualization as PNG
     */
    exportPNG() {
        if (this.mindmapVisualizer && this.mindmapVisualizer.svgElement) {
            const svgData = new XMLSerializer().serializeToString(this.mindmapVisualizer.svgElement);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.fillStyle = '#1f2937';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0);
                
                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'dependency-mindmap.png';
                    link.click();
                    URL.revokeObjectURL(url);
                });
            };
            
            img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
        }
    }



    /**
     * Show node details in the details panel
     * @param {Object} node - Node data
     */
    showNodeDetails(node) {
        if (!this.detailsPanel) return;
        
        console.log('App.showNodeDetails - Received node data:', node);
        console.log('App.showNodeDetails - Node imports:', node.imports);
        console.log('App.showNodeDetails - Node exports:', node.exports);
        console.log('App.showNodeDetails - Node functions:', node.functions);
        console.log('App.showNodeDetails - Node events:', node.events);
        
        // Update panel title
        const panelTitle = this.detailsPanel.querySelector('.details-panel-header h3');
        if (panelTitle) {
            panelTitle.textContent = node.name || node.text || 'Unknown';
        }
        
        // Update panel content
        const panelContent = this.detailsPanel.querySelector('.details-panel-content');
        if (panelContent) {
            let html = '';
            
            // File info
            html += `
                <div class="details-section">
                    <h3 class="section-title">File Info</h3>
                    <div class="section-content">
                        <p><strong>Path:</strong> ${node.path || node.id || 'Unknown'}</p>
                        <p><strong>Type:</strong> ${node.type || 'Unknown'}</p>
                        <p><strong>Name:</strong> ${node.name || node.text || 'Unknown'}</p>
                    </div>
                </div>
            `;
            
            // Imports
            if (node.imports && node.imports.length > 0) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title imports-title">Imports (${node.imports.length})</h3>
                        <ul class="section-content">
                            ${node.imports.map(imp => `
                                <li data-path="${imp.path || imp.name || imp}">
                                    ${typeof imp === 'string' ? imp : (imp.path || imp.name || 'Unknown').split('/').pop()}
                                    ${imp.names && imp.names.length > 0
                                        ? `<span class="import-names">${imp.names.join(', ')}</span>`
                                        : ''}
                                    ${imp.line ? `<span class="line-info">(line ${imp.line})</span>` : ''}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // Exports
            if (node.exports && node.exports.length > 0) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title exports-title">Exports (${node.exports.length})</h3>
                        <ul class="section-content">
                            ${node.exports.map(exp => `
                                <li>
                                    ${typeof exp === 'string' ? exp : (exp.name || 'Unknown')}
                                    ${exp.isDefault ? '<span class="export-default">(default)</span>' : ''}
                                    ${exp.exportType ? `<span class="export-type">(${exp.exportType})</span>` : ''}
                                    ${exp.line ? `<span class="line-info">(line ${exp.line})</span>` : ''}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // Functions
            if (node.functions && node.functions.length > 0) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title functions-title">Functions (${node.functions.length})</h3>
                        <ul class="section-content">
                            ${node.functions.map(func => `
                                <li>
                                    ${typeof func === 'string' ? func : (func.name || 'Anonymous')}
                                    ${func.type === 'class' ? '(class)' : ''}
                                    ${func.params ? `(${func.params.join(', ')})` : ''}
                                    ${func.line ? `<span class="line-info">(line ${func.line})</span>` : ''}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // Variables
            if (node.variables && node.variables.length > 0) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title variables-title">Variables (${node.variables.length})</h3>
                        <ul class="section-content">
                            ${node.variables.map(variable => `
                                <li>
                                    ${typeof variable === 'string' ? variable : (variable.name || 'Unknown')}
                                    ${variable.type ? `<span class="var-type">(${variable.type})</span>` : ''}
                                    ${variable.line ? `<span class="line-info">(line ${variable.line})</span>` : ''}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // Classes
            if (node.classes && node.classes.length > 0) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title classes-title">Classes (${node.classes.length})</h3>
                        <ul class="section-content">
                            ${node.classes.map(cls => `
                                <li>
                                    ${typeof cls === 'string' ? cls : (cls.name || 'Unknown')}
                                    ${cls.line ? `<span class="line-info">(line ${cls.line})</span>` : ''}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // Events
            if (node.events && node.events.length > 0) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title events-title">Events (${node.events.length})</h3>
                        <ul class="section-content">
                            ${node.events.map(event => `
                                <li>
                                    ${typeof event === 'string' ? event : (event.name || 'Unknown')}
                                    ${event.type ? `<span class="event-type">(${event.type})</span>` : ''}
                                    ${event.handler ? `<span class="event-handler">→ ${event.handler}</span>` : ''}
                                    ${event.line ? `<span class="line-info">(line ${event.line})</span>` : ''}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // Show message if no detailed data
            if ((!node.imports || node.imports.length === 0) &&
                (!node.exports || node.exports.length === 0) &&
                (!node.functions || node.functions.length === 0) &&
                (!node.variables || node.variables.length === 0) &&
                (!node.classes || node.classes.length === 0) &&
                (!node.events || node.events.length === 0)) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title">No Detailed Data</h3>
                        <div class="section-content">
                            <p>No imports, exports, functions, variables, classes, or events found for this file.</p>
                        </div>
                    </div>
                `;
            }
            
            panelContent.innerHTML = html;
            
            // Add click handlers for imports
            panelContent.querySelectorAll('.details-section li[data-path]').forEach(item => {
                item.addEventListener('click', () => {
                    const path = item.dataset.path;
                    const node = this.dataModel.getNodeByPath(path);
                    if (node) {
                        this.dataModel.setSelectedNode(node.id);
                        this.render();
                        this.showNodeDetails(node);
                    }
                });
            });
        }
        
        // Show panel
        this.showDetailsPanel();
    }

    /**
     * Show the details panel
     */
    showDetailsPanel() {
        if (!this.detailsPanel) return;
        
        DOMUtils.toggleClass(this.detailsPanel, 'open', true);
        DOMUtils.toggleClass(document.querySelector('.content-scroll-area'), 'panel-open', true);
    }

    /**
     * Hide the details panel
     */
    hideDetailsPanel() {
        if (!this.detailsPanel) return;
        
        DOMUtils.toggleClass(this.detailsPanel, 'open', false);
        DOMUtils.toggleClass(document.querySelector('.content-scroll-area'), 'panel-open', false);
    }

    /**
     * Show loading indicator
     * @param {string} message - Loading message
     */
    showLoadingIndicator(message = 'Loading...') {
        let loadingOverlay = document.querySelector('.loading-overlay');
        
        if (!loadingOverlay) {
            loadingOverlay = DOMUtils.createElement('div', {
                class: 'loading-overlay'
            });
            
            const spinner = DOMUtils.createElement('div', {
                class: 'spinner'
            });
            
            const loadingText = DOMUtils.createElement('div', {
                class: 'loading-text',
                style: {
                    color: '#fff',
                    marginTop: '15px',
                    fontSize: '14px'
                }
            }, message);
            
            loadingOverlay.appendChild(spinner);
            loadingOverlay.appendChild(loadingText);
            document.body.appendChild(loadingOverlay);
        } else {
            const loadingText = loadingOverlay.querySelector('.loading-text');
            if (loadingText) {
                loadingText.textContent = message;
            }
        }
    }

    /**
     * Update loading indicator message
     * @param {string} message - New loading message
     */
    updateLoadingIndicator(message) {
        const loadingText = document.querySelector('.loading-overlay .loading-text');
        if (loadingText) {
            loadingText.textContent = message;
        }
    }

    /**
     * Hide loading indicator
     */
    hideLoadingIndicator() {
        const loadingOverlay = document.querySelector('.loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.remove();
        }
    }

    /**
     * Show error message
     * @param {string} message - Error message
     */
    showError(message) {
        const errorContainer = DOMUtils.createElement('div', {
            class: 'error-message',
            style: {
                position: 'fixed',
                top: '20px',
                left: '50%',
                transform: 'translateX(-50%)',
                backgroundColor: 'rgba(244, 67, 54, 0.9)',
                color: '#fff',
                padding: '10px 20px',
                borderRadius: '4px',
                boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
                zIndex: '9999',
                maxWidth: '80%'
            }
        }, message);
        
        document.body.appendChild(errorContainer);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            errorContainer.remove();
        }, 5000);
    }

    /**
     * Get code snippet for a file at a specific line
     * @param {string} filePath - Path to the file
     * @param {number} lineNumber - Line number to get snippet for
     * @returns {Promise<Object>} - Promise resolving to snippet data
     */
    getCodeSnippet(filePath, lineNumber) {
        return new Promise((resolve, reject) => {
            try {
                // Check if we have scan results with file contents
                if (window.scanResults && window.scanResults.fileContents && window.scanResults.fileContents[filePath]) {
                    const content = window.scanResults.fileContents[filePath];
                    const lines = content.split('\n');
                    
                    // Get 5 lines before and after the target line
                    const startLine = Math.max(1, lineNumber - 5);
                    const endLine = Math.min(lines.length, lineNumber + 5);
                    
                    const snippet = lines.slice(startLine - 1, endLine).join('\n');
                    
                    resolve({
                        snippet: snippet,
                        startLine: startLine
                    });
                } else {
                    reject(new Error('File content not available'));
                }
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Get the currently selected node path
     * @returns {string|null} - Path of the currently selected node
     */
    getCurrentSelectedNodePath() {
        if (this.currentVisualizer && this.currentVisualizer.selectedNode) {
            return this.currentVisualizer.selectedNode.path || this.currentVisualizer.selectedNode.id;
        }
        return null;
    }

    /**
     * Select a node and open file details
     * @param {string} filePath - Path of the file to select
     * @param {string} focusProperty - Optional property to focus on
     */
    selectNodeAndOpenFileDetails(filePath, focusProperty = null) {
        if (!this.dataModel) return;
        
        // Find the node by path
        const nodes = this.dataModel.getNodes();
        const node = nodes.find(n => n.path === filePath || n.id === filePath);
        
        if (node) {
            // Set as selected in data model
            this.dataModel.setSelectedNode(node.id);
            
            // Update visualization
            this.render();
            
            // Open details panel
            if (window.openDetailsPanel) {
                window.openDetailsPanel(node, focusProperty);
            }
        }
    }
}

// Export the application class
export default DependencyVisualizerApp;
